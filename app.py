from flask import Flask, render_template
from config import db, SQLALCHEMY_DATABASE_URI
from routers import *
# Import all models to register them with SQLAlchemy
import models

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = SQLALCHEMY_DATABASE_URI
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db.init_app(app)

app.register_blueprint(sinhVienBPrint, url_prefix="/api")
app.register_blueprint(giangVienBPrint, url_prefix="/api")

if __name__ == "__main__":
    with app.app_context():
        db.create_all()  # tạo bảng nếu chưa có
    app.run(debug=True)

from flask import Blueprint, jsonify, request
from controllers.giangvien_controller import (
    get_all as get_all_gv, create_new as create_new_gv,
    update_information as update_information_gv, delete_GV as delete_GV_controller,
    search_MaGV as search_MaGV_controller, get_all_with_subject_count
)

giangVienBPrint = Blueprint("GiangVien", __name__)

@giangVienBPrint.route("/GiangVien", methods=["GET"])
def get_all_giangvien():
    """
    Lấy danh sách tất cả giảng viên
    Returns: JSON array chứa thông tin tất cả giảng viên
    """
    try:
        gv_list = get_all_gv()
        return jsonify(gv_list), 200
    except Exception as e:
        return jsonify({"error": f"Lỗi khi lấy danh sách giảng viên: {str(e)}"}), 500

@giangVienBPrint.route("/GiangVien", methods=["POST"])
def create_new_giangvien():
    """
    Tạo giảng viên mới
    Expects: JSON body với MaGV và TenGV
    Returns: JSON object của giảng viên vừa tạo
    """
    try:
        data = request.get_json()  # Lấy dữ liệu JSON từ request body
        if not data:
            return jsonify({"error": "Không có dữ liệu được gửi"}), 400

        # Kiểm tra các trường bắt buộc
        if not data.get("MaGV") or not data.get("TenGV"):
            return jsonify({"error": "MaGV và TenGV là bắt buộc"}), 400

        gv = create_new_gv(data)
        return jsonify(gv), 201
    except Exception as e:
        return jsonify({"error": f"Lỗi khi tạo giảng viên: {str(e)}"}), 500

@giangVienBPrint.route("/GiangVien/<MaGV>", methods=["PUT"])
def update_giangvien(MaGV):
    """
    Cập nhật thông tin giảng viên
    Args: MaGV - Mã giảng viên cần cập nhật
    Expects: JSON body với dữ liệu cần cập nhật
    Returns: JSON object của giảng viên đã cập nhật
    """
    try:
        data = request.get_json()  # Lấy dữ liệu JSON từ request body
        if not data:
            return jsonify({"error": "Không có dữ liệu được gửi"}), 400

        gv = update_information_gv(MaGV=MaGV, data=data)
        if gv:
            return jsonify(gv), 200
        else:
            return jsonify({"error": "Không tìm thấy giảng viên"}), 404
    except Exception as e:
        return jsonify({"error": f"Lỗi khi cập nhật giảng viên: {str(e)}"}), 500

@giangVienBPrint.route("/GiangVien/<MaGV>", methods=["DELETE"])
def delete_giangvien(MaGV):
    """
    Xóa giảng viên
    Args: MaGV - Mã giảng viên cần xóa
    Returns: JSON message xác nhận xóa thành công hoặc lỗi
    """
    try:
        check = delete_GV_controller(MaGV=MaGV)
        if check:
            return jsonify({"message": "Đã xóa thành công giảng viên"}), 200
        return jsonify({"error": "Không tìm thấy giảng viên"}), 404
    except Exception as e:
        return jsonify({"error": f"Lỗi khi xóa giảng viên: {str(e)}"}), 500

@giangVienBPrint.route("/GiangVien/Search", methods=["GET"])
def search_giangvien():
    """
    Tìm kiếm giảng viên theo mã
    Query params: MaGV - Mã giảng viên cần tìm (có thể là một phần)
    Returns: JSON array chứa các giảng viên phù hợp
    """
    try:
        MaGV = request.args.get('MaGV')  # Lấy tham số từ query string
        if not MaGV:
            return jsonify({"error": "Tham số MaGV là bắt buộc"}), 400

        gv_list = search_MaGV_controller(MaGV=MaGV)
        return jsonify(gv_list), 200
    except Exception as e:
        return jsonify({"error": f"Lỗi khi tìm kiếm giảng viên: {str(e)}"}), 500

@giangVienBPrint.route("/GiangVien/WithSubjectCount", methods=["GET"])
def get_giangvien_with_subject_count():
    """
    Lấy danh sách tất cả giảng viên kèm theo số lượng môn học đang giảng dạy
    HTTP Status Codes:
        200: Success - Trả về danh sách giảng viên với subject count
        500: Internal Server Error - Lỗi database hoặc server
    """
    try:
        # Gọi controller function để lấy dữ liệu
        gv_list_with_count = get_all_with_subject_count()

        # Trả về JSON response với HTTP status 200
        return jsonify(gv_list_with_count), 200

    except Exception as e:
        # Log error và trả về error response với HTTP status 500
        return jsonify({
            "error": f"Lỗi khi lấy danh sách giảng viên với số môn học: {str(e)}"
        }), 500
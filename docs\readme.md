À ok, mình giải thích rõ hai cái này luôn — vì `data["field"]` và `data.get("field")` **giống nhau ở mục đích lấy giá trị** nhưng khác **hành vi khi key không tồn tại**.
D:/Project/QuanLyTruongHoc/venv/Scripts/Activate.ps1
---

## **1. `data["key"]` – Truy cập trực tiếp**

### **Cú pháp**

```python
value = data["key"]
```

### **Đặc điểm**

* Nếu **key tồn tại** → trả về giá trị.
* Nếu **key không tồn tại** → **KeyError** (lỗi ngay lập tức).

### **Ví dụ**

```python
data = {"MaSV": "SV01", "TenSV": "<PERSON><PERSON><PERSON>"}

print(data["MaSV"])    # SV01
print(data["Email"])   # LỖI: KeyError: 'Email'
```

---

## **2. `data.get("key", default)` – Truy cập an toàn**

### **Cú pháp**

```python
value = data.get("key", default_value)
```

### **Đặc điểm**

* Nếu **key tồn tại** → trả về giá trị.
* Nếu **key không tồn tại** → trả về **default\_value** (mặc định `None` nếu không truyền gì).

### **Ví dụ**

```python
data = {"MaSV": "SV01", "TenSV": "Nguyen Van A"}

print(data.get("MaSV"))           # SV01
print(data.get("Email"))          # None
print(data.get("Email", "No Mail"))  # "No Mail"
```

---

## **So sánh nhanh**

| Viết              | Khi thiếu key  | Có thể đặt giá trị mặc định? | Dùng khi                                  |
| ----------------- | -------------- | ---------------------------- | ----------------------------------------- |
| `data["key"]`     | Lỗi `KeyError` | Không                        | Key **bắt buộc** phải có                  |
| `data.get("key")` | Trả `None`     | Có thể đặt mặc định          | Key **tùy chọn** (nullable hoặc optional) |

---

## **Ứng dụng trong controller**

* **Tạo mới (Create):**

  * Field bắt buộc → dùng `data["field"]`
  * Field nullable → dùng `data.get("field")`

* **Cập nhật (Update):**

  * Dùng `data.get("field", sv.field)` → update nếu có giá trị mới, giữ nguyên nếu không gửi.

---

Bạn có muốn mình **vẽ sơ đồ minh họa** (data dict → lấy bằng \[] vs get) cho dễ hình dung không?
Hay mình **làm ví dụ thực tế trong API** (client gửi thiếu field xem kết quả ra sao)?

"""
Script để kiểm tra dữ liệu trực tiếp trong SQL Server
"""
from config import get_db_connection

def check_database_directly():
    """
    Kiểm tra dữ liệu trực tiếp từ SQL Server
    """
    try:
        print("=== KIỂM TRA KẾT NỐI SQL SERVER ===")
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Kiểm tra các bảng có tồn tại không
            print("\n1. Kiểm tra các bảng trong database:")
            cursor.execute("""
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
                ORDER BY TABLE_NAME
            """)
            
            tables = cursor.fetchall()
            if tables:
                print("Các bảng tìm thấy:")
                for table in tables:
                    print(f"  - {table[0]}")
            else:
                print("Không tìm thấy bảng nào!")
                return
            
            # Kiểm tra dữ liệu trong bảng SinhVien
            print("\n2. Kiểm tra dữ liệu trong bảng SinhVien:")
            cursor.execute("SELECT COUNT(*) FROM SinhVien")
            count = cursor.fetchone()[0]
            print(f"Số lượng sinh viên: {count}")
            
            if count > 0:
                print("\nDữ liệu sinh viên:")
                cursor.execute("SELECT MaSV, TenSV, Email FROM SinhVien ORDER BY MaSV")
                students = cursor.fetchall()
                for student in students:
                    print(f"  - {student[0]}: {student[1]} ({student[2]})")
            
            # Kiểm tra dữ liệu trong bảng GiangVien
            print("\n3. Kiểm tra dữ liệu trong bảng GiangVien:")
            cursor.execute("SELECT COUNT(*) FROM GiangVien")
            count = cursor.fetchone()[0]
            print(f"Số lượng giảng viên: {count}")
            
            if count > 0:
                print("\nDữ liệu giảng viên:")
                cursor.execute("SELECT MaGV, TenGV FROM GiangVien ORDER BY MaGV")
                teachers = cursor.fetchall()
                for teacher in teachers:
                    print(f"  - {teacher[0]}: {teacher[1]}")
                    
    except Exception as e:
        print(f"Lỗi khi kiểm tra database: {str(e)}")

if __name__ == "__main__":
    check_database_directly()
